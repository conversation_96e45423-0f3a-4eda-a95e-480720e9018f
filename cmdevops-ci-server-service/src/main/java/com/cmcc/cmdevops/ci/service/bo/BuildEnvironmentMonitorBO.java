package com.cmcc.cmdevops.ci.service.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class BuildEnvironmentMonitorBO {
    /**
     * 构建环境Id
     */
    private Integer environmentId;

    /**
     * 命名空间总数
     */
    private Integer totalNamespaceCount;

    /**
     * 命名空间正常数量
     */
    private Integer normalNamespaceCount;

    /**
     * 命名空间异常数量
     */
    private Integer abnormalNamespaceCount;

    /**
     * 构建任务总数
     */
    private Integer totalTaskCount;

    /**
     * 构建任务执行数量
     */
    private Integer runningTaskCount;

    /**
     * 构建任务排队数量
     */
    private Integer queuedTaskCount;

    /**
     * 集群CPU分配率
     */
    private Double clusterCpuUsageRate;

    /**
     * 集群内存分配率
     */
    private Double clusterMemoryUsageRate;

    /**
     * minio存储分配率
     */
    private Double minioStorageUsageRate;

    /**
     * minio存储总量
     */
    private Long minioTotalStorage;

    /**
     * minio存储使用量
     */
    private Long minioUsedStorage;

    /**
     * 命名空间实时运行任务数量统计
     */
    private List<NamespaceTaskBO> namespaceTasks;

    /**
     * 命名空间资源实时使用情况列表
     */
    private List<NamespaceMonitorBO> namespaceMonitors;


    /**
     * 命名空间任务信息
     */
    @Data
    public class NamespaceTaskBO {
        /**
         * 命名空间
         */
        private String namespace;
        /**
         * 任务总数
         */
        private Integer totalTaskCount;
        /**
         * 运行中任务数
         */
        private Integer runningTaskCount;
        /**
         * 排队中任务数
         */
        private Integer queuedTaskCount;
    }

    /**
     * 命名空间监控信息
     */
    @Data
    public class NamespaceMonitorBO {
        /**
         * 命名空间
         */
        private String namespace;
        /**
         * CPU使用量(核)
         */
        private String usedCpu;
        /**
         * 内存使用量(GB)
         */
        private String usedMemory;
        /**
         * CPU使用量限额(核)
         */
        private String limitedCpu;
        /**
         * 内存使用量限额(GB)
         */
        private String limitedMemory;
        /**
         * CPU分配率
         */
        private Double cpuUsageRate;
        /**
         * 内存分配率
         */
        private Double memoryUsageRate;
        /**
         * CPU使用状态
         */
        private String cpuStatus;
        /**
         * 内存使用状态
         */
        private String memoryStatus;
        /**
         * 当前任务数
         */
        private Integer taskCount;
    }
}
